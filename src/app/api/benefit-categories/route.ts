import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/local-db'
import { requireWebUIOrAdmin } from '@/lib/api-access-control'

// GET /api/benefit-categories - Get all benefit categories that have benefits (web UI or admin)
export async function GET(request: NextRequest) {
  try {
    // Allow web UI access for all users, block direct API access except for admins
    await requireWebUIOrAdmin(request)

    const result = await query(`
      SELECT
        bc.id,
        bc.name,
        bc.display_name,
        bc.description,
        bc.icon,
        bc.sort_order,
        COUNT(b.id) as benefit_count
      FROM benefit_categories bc
      LEFT JOIN benefits b ON bc.id = b.category_id
      GROUP BY bc.id
      HAVING COUNT(b.id) > 0
      ORDER BY bc.sort_order ASC, bc.display_name ASC
    `)

    return NextResponse.json(result.rows)

  } catch (error) {
    console.error('Error fetching benefit categories:', error)
    return NextResponse.json(
      { error: 'Failed to fetch benefit categories' },
      { status: 500 }
    )
  }
}
