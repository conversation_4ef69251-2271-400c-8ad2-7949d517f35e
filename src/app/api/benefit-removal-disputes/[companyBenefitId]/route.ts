import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/auth'
import { requireWebUIOrAdmin } from '@/lib/api-access-control'
import { query } from '@/lib/local-db'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyBenefitId: string }> }
) {
  try {
    // Prevent direct API scraping of company/benefit data through disputes
    await requireWebUIOrAdmin(request)

    const user = await getCurrentUser()
    const { companyBenefitId } = await params

    // For unauthenticated users, we can still return basic info but canDispute will be false
    const userId = user?.id

    // Get benefit information and check authorization
    const benefitResult = await query(`
      SELECT 
        cb.id,
        cb.is_verified,
        c.id as company_id,
        c.name as company_name,
        c.domain as company_domain,
        b.name as benefit_name
      FROM company_benefits cb
      JOIN companies c ON cb.company_id = c.id
      JOIN benefits b ON cb.benefit_id = b.id
      WHERE cb.id = $1
    `, [companyBenefitId])

    if (benefitResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'Company benefit not found' },
        { status: 404 }
      )
    }

    const benefit = benefitResult.rows[0]

    // Check domain authorization - only possible if user is authenticated
    let canDispute = false
    if (user && user.email) {
      const userDomain = user.email.split('@')[1]
      canDispute = userDomain === benefit.company_domain && benefit.is_verified
    }

    // Get dispute statistics for this benefit
    const disputeStatsResult = await query(`
      SELECT 
        status,
        COUNT(*) as count
      FROM benefit_removal_disputes
      WHERE company_benefit_id = $1
      GROUP BY status
    `, [companyBenefitId])

    const stats = {
      pending: 0,
      approved: 0,
      rejected: 0,
      total: 0
    }

    disputeStatsResult.rows.forEach(row => {
      const status = row.status as keyof typeof stats
      if (status !== 'total') {
        stats[status] = parseInt(row.count)
        stats.total += parseInt(row.count)
      }
    })

    // Check if current user has already submitted a dispute (only if authenticated)
    let userDispute = null
    if (userId) {
      const userDisputeResult = await query(
        'SELECT * FROM benefit_removal_disputes WHERE company_benefit_id = $1 AND user_id = $2',
        [companyBenefitId, userId]
      )
      userDispute = userDisputeResult.rows[0] || null
    }

    // Get recent disputes (for display purposes)
    const recentDisputesResult = await query(`
      SELECT 
        brd.id,
        brd.reason,
        brd.status,
        brd.created_at,
        u.email as user_email,
        u.first_name as user_first_name,
        u.last_name as user_last_name
      FROM benefit_removal_disputes brd
      LEFT JOIN users u ON brd.user_id = u.id
      WHERE brd.company_benefit_id = $1
      ORDER BY brd.created_at DESC
      LIMIT 10
    `, [companyBenefitId])

    return NextResponse.json({
      benefit: {
        id: benefit.id,
        name: benefit.benefit_name,
        company_name: benefit.company_name,
        is_verified: benefit.is_verified
      },
      canDispute,
      userDispute,
      stats,
      recentDisputes: recentDisputesResult.rows,
      requiresApprovals: 2 // Number of approved disputes needed for removal
    })

  } catch (error) {
    console.error('Error fetching benefit dispute status:', error)
    return NextResponse.json(
      { error: 'Failed to fetch dispute status' },
      { status: 500 }
    )
  }
}
