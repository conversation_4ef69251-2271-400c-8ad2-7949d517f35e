import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/local-db'
import { requireAuth } from '@/lib/auth'
import { requireWebUIOrAdmin } from '@/lib/api-access-control'
import { createBenefit } from '@/lib/database'

export async function GET(request: NextRequest) {
  try {
    // Allow web UI access for all users, block direct API access except for admins
    await requireWebUIOrAdmin(request)

    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')
    const categoryId = searchParams.get('categoryId')

    let whereClause = ''
    const params: string[] = []

    if (categoryId) {
      whereClause = 'WHERE b.category_id = $1'
      params.push(categoryId)
    } else if (category) {
      // Support legacy category names for backward compatibility
      whereClause = 'WHERE bc.name = $1'
      params.push(category)
    }

    const sql = `
      SELECT
        b.*,
        bc.name as category_name,
        bc.display_name as category_display_name,
        bc.icon as category_icon
      FROM benefits b
      LEFT JOIN benefit_categories bc ON b.category_id = bc.id
      ${whereClause}
      ORDER BY b.name
    `

    const result = await query(sql, params)

    return NextResponse.json({ benefits: result.rows })
  } catch (error) {
    console.error('Error fetching benefits:', error)
    return NextResponse.json(
      { error: 'Failed to fetch benefits' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    await requireAuth()

    const body = await request.json()
    const { name, category, icon, description } = body

    if (!name || !category) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Convert category name to category_id
    const categoryResult = await query(
      'SELECT id FROM benefit_categories WHERE name = $1',
      [category]
    )

    if (categoryResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'Invalid category provided' },
        { status: 400 }
      )
    }

    const categoryId = categoryResult.rows[0].id

    const benefit = await createBenefit({
      name,
      category_id: categoryId,
      icon,
      description: description || '',
    })

    return NextResponse.json(benefit, { status: 201 })
  } catch (error) {
    console.error('Error creating benefit:', error)
    return NextResponse.json(
      { error: 'Failed to create benefit' },
      { status: 500 }
    )
  }
}
