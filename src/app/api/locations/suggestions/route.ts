import { NextRequest, NextResponse } from 'next/server'
import { getLocationSuggestions } from '@/lib/location-normalization'
import { requireWebUIOrAdmin } from '@/lib/api-access-control'

export async function GET(request: NextRequest) {
  try {
    // Allow web UI access for all users, block direct API access except for admins
    await requireWebUIOrAdmin(request)

    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q')
    const limitParam = searchParams.get('limit')
    
    if (!query || query.trim().length < 2) {
      return NextResponse.json({
        suggestions: [],
        message: 'Query must be at least 2 characters'
      })
    }

    const limit = limitParam ? parseInt(limitParam, 10) : 10
    const suggestions = await getLocationSuggestions(query.trim(), limit)
    
    return NextResponse.json({
      suggestions,
      query: query.trim(),
      count: suggestions.length
    })
  } catch (error) {
    console.error('Error getting location suggestions:', error)
    return NextResponse.json(
      { 
        error: 'Failed to get location suggestions',
        suggestions: []
      },
      { status: 500 }
    )
  }
}
