import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/local-db'
import { requireWeb<PERSON>OrAdmin } from '@/lib/api-access-control'

export async function GET(request: NextRequest) {
  try {
    // Allow web UI access for all users, block direct API access except for admins
    await requireWebUIOrAdmin(request)

    // Get all unique industries with count of companies in each industry
    const sql = `
      SELECT 
        industry,
        COUNT(*) as company_count
      FROM companies
      WHERE industry IS NOT NULL AND industry != ''
      GROUP BY industry
      ORDER BY company_count DESC, industry ASC
    `

    const result = await query(sql)
    
    const industries = result.rows.map(row => ({
      value: row.industry,
      label: row.industry,
      count: parseInt(row.company_count)
    }))

    return NextResponse.json(industries)
  } catch (error) {
    console.error('Error fetching industry options:', error)
    return NextResponse.json(
      { error: 'Failed to fetch industry options' },
      { status: 500 }
    )
  }
}
