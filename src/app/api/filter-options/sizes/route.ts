import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/local-db'
import { requireWeb<PERSON>OrAdmin } from '@/lib/api-access-control'

export async function GET(request: NextRequest) {
  try {
    // Allow web UI access for all users, block direct API access except for admins
    await requireWebUIOrAdmin(request)

    const result = await query(`
      SELECT 
        size as value,
        size as label,
        COUNT(*) as count
      FROM companies 
      WHERE size IS NOT NULL 
      GROUP BY size 
      ORDER BY 
        CASE size
          WHEN 'startup' THEN 1
          WHEN 'small' THEN 2
          WHEN 'medium' THEN 3
          WHEN 'large' THEN 4
          WHEN 'enterprise' THEN 5
          ELSE 6
        END
    `)

    // Map the database values to user-friendly labels
    const sizeLabels = {
      startup: 'Startup',
      small: 'Small (1-50 employees)',
      medium: 'Medium (51-200 employees)', 
      large: 'Large (201-1000 employees)',
      enterprise: 'Enterprise (1000+ employees)'
    }

    const sizes = result.rows.map(row => ({
      value: row.value,
      label: sizeLabels[row.value as keyof typeof sizeLabels] || row.value,
      count: parseInt(row.count)
    }))

    return NextResponse.json(sizes)
  } catch (error) {
    console.error('Error fetching size options:', error)
    return NextResponse.json(
      { error: 'Failed to fetch size options' },
      { status: 500 }
    )
  }
}
