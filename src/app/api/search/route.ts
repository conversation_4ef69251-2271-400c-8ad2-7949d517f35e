import { NextRequest, NextResponse } from 'next/server'
import { searchCompanies } from '@/lib/database'
import { requireWebUIOrAdmin } from '@/lib/api-access-control'

export async function GET(request: NextRequest) {
  try {
    // Allow web UI access for all users, block direct API access except for admins
    await requireWebUIOrAdmin(request)

    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q')
    
    if (!query || query.trim().length < 2) {
      return NextResponse.json(
        { error: 'Search query must be at least 2 characters' },
        { status: 400 }
      )
    }

    const companies = await searchCompanies(query.trim())
    
    return NextResponse.json(companies)
  } catch (error) {
    console.error('Error searching companies:', error)
    return NextResponse.json(
      { error: 'Failed to search companies' },
      { status: 500 }
    )
  }
}
