import { NextRequest } from 'next/server'
import { getCurrentUser, isAdmin } from './auth'

/**
 * Check if a request is coming from the web UI vs direct API access
 */
export function isWebUIRequest(request: NextRequest): boolean {
  // Check for Next.js internal requests (SSR, client-side navigation)
  const userAgent = request.headers.get('user-agent') || ''
  const referer = request.headers.get('referer') || ''
  const origin = request.headers.get('origin') || ''
  
  // Allow requests from the same origin (web UI)
  const url = new URL(request.url)
  const requestOrigin = `${url.protocol}//${url.host}`
  
  // Check if request is from same origin
  if (origin === requestOrigin) {
    return true
  }
  
  // Check if referer is from same origin
  if (referer.startsWith(requestOrigin)) {
    return true
  }
  
  // Check for Next.js specific headers that indicate internal requests
  const nextHeaders = [
    'x-nextjs-data',
    'x-middleware-prefetch',
    'x-middleware-invoke'
  ]
  
  for (const header of nextHeaders) {
    if (request.headers.get(header)) {
      return true
    }
  }
  
  // Check for browser-like user agents (not curl, wget, etc.)
  const browserPatterns = [
    /Mozilla/,
    /Chrome/,
    /Safari/,
    /Firefox/,
    /Edge/,
    /Opera/
  ]
  
  const isBrowserLike = browserPatterns.some(pattern => pattern.test(userAgent))
  
  // If it looks like a browser and has a referer from our domain, allow it
  if (isBrowserLike && (referer.includes(url.host) || referer === '')) {
    return true
  }
  
  return false
}

/**
 * Check if a request should be allowed to access API data
 * Returns true if:
 * 1. Request is from web UI
 * 2. User is an admin
 */
export async function canAccessAPIData(request: NextRequest): Promise<{
  allowed: boolean
  reason: 'web-ui' | 'admin' | 'blocked'
}> {
  // Always allow web UI requests
  if (isWebUIRequest(request)) {
    return { allowed: true, reason: 'web-ui' }
  }
  
  // Check if user is admin for direct API access
  try {
    const user = await getCurrentUser()
    if (user && await isAdmin()) {
      return { allowed: true, reason: 'admin' }
    }
  } catch (error) {
    // Ignore auth errors for this check
  }
  
  // Block all other direct API access
  return { allowed: false, reason: 'blocked' }
}

/**
 * Middleware function to protect API endpoints
 * Use this in API routes to allow web UI access but block direct API calls
 */
export async function requireWebUIOrAdmin(request: NextRequest): Promise<void> {
  const access = await canAccessAPIData(request)
  
  if (!access.allowed) {
    throw new Error('Direct API access not allowed. Please use the web interface.')
  }
}
